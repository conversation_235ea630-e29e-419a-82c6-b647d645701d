import os
import logging
import requests
import json
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
from .auth_serializers import RegisterSerializer, GoogleAuthSerializer
from .models import Instructor

# Set up logger
logger = logging.getLogger(__name__)

User = get_user_model()


def get_tokens_for_user(user):
    """
    Generate JWT tokens for a user
    """
    refresh = RefreshToken.for_user(user)
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
    }


def generate_api_key(user_id, admin_name=None, admin_password=None):
    """
    Generate API key from external service

    This function is prepared to make an actual HTTP request to an external API service.
    The commented code below shows how to make the request when ready to implement.
    Currently, it returns a simulated API key for development purposes.

    Environment variables:
    - AI_SERVICE_URL: Base URL of the AI service
    - ADMIN_NAME: Admin name for authentication
    - ADMIN_PASSWORD: Admin password for authentication
    """
    base_url = os.getenv('AI_SERVICE_URL')
    # endpoint = os.getenv('API_KEY_SERVICE_ENDPOINT', '/api/permissions/')
    url = f"{base_url.rstrip('/')}/api/permissions/"  # Ensure proper URL formatting
    admin = admin_name or os.getenv('ADMIN_NAME')
    password = admin_password or os.getenv('ADMIN_PASSWORD')

    # Prepare the payload for the API request
    # This is used in the commented HTTP request code below
    payload = {
        "User_ID": user_id,
        "Admin": admin,
        "PWD": password
    }

    # Prepare headers for the API request
    # This is used in the commented HTTP request code below
    headers = {
        'Content-Type': 'application/json',
    }

    try:
        # Make an actual HTTP request to the external service
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        api_key = response.json().get('api_key')
        logger.info(f"Successfully generated API key for user ID: {user_id}")
        return api_key
    except requests.RequestException as e:
        logger.error(f"Error calling external API service: {str(e)}")
        return None


class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            # Generate API key from external service
            api_key = generate_api_key(user_id=user.id)

            if api_key:
                # Set API key on user model
                user.api_key = api_key
                user.save()
                # The API key will be synchronized to the instructor model via the User.save() method
            else:
                # API key generation failed
                logger.error(f"Failed to generate API key for user ID: {user.id}")
                return Response(
                    {"error": "Failed to generate API key. Please try again later."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            tokens = get_tokens_for_user(user)

            # Get the instructor_id if available
            instructor_id = None
            try:
                if hasattr(user, 'instructor_profile'):
                    instructor_id = user.instructor_profile.id
            except Exception as e:
                logger.error(f"Error getting instructor_id: {str(e)}")

            return Response({
                **tokens,
                'email': user.email,
                'username': user.username,
                'department': user.department,
                'api_key': user.api_key,
                'instructor_id': instructor_id
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')

        if not email or not password:
            return Response({'error': 'Please provide both email and password'},
                            status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'error': 'Invalid credentials'},
                            status=status.HTTP_401_UNAUTHORIZED)

        if not user.check_password(password):
            return Response({'error': 'Invalid credentials'},
                            status=status.HTTP_401_UNAUTHORIZED)

        tokens = get_tokens_for_user(user)

        # Get the instructor_id if available
        instructor_id = None
        try:
            if hasattr(user, 'instructor_profile'):
                instructor_id = user.instructor_profile.id
        except Exception as e:
            logger.error(f"Error getting instructor_id: {str(e)}")

        return Response({
            **tokens,
            'email': user.email,
            'username': user.username,
            'department': user.department,
            'api_key': user.api_key,
            'instructor_id': instructor_id
        })


class GoogleLoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = GoogleAuthSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        token = serializer.validated_data['token']

        try:
            # Verify the Google token
            idinfo = id_token.verify_oauth2_token(
                token, google_requests.Request(), settings.GOOGLE_CLIENT_ID)

            # Get user email from token
            email = idinfo['email']

            # Check if user exists
            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                # Create a new user
                username = email.split('@')[0]
                user = User.objects.create_user(
                    username=username,
                    email=email,
                )

                # Create instructor profile
                Instructor.objects.create(
                    user=user,
                    name=username
                )

                # Generate API key from external service
                api_key = generate_api_key(user_id=user.id)

                if api_key:
                    # Set API key on user model
                    user.api_key = api_key
                    user.save()
                    # The API key will be synchronized to the instructor model via the User.save() method
                else:
                    # API key generation failed
                    logger.error(f"Failed to generate API key for user ID: {user.id}")
                    return Response(
                        {"error": "Failed to generate API key. Please try again later."},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            tokens = get_tokens_for_user(user)

            # Get the instructor_id if available
            instructor_id = None
            try:
                if hasattr(user, 'instructor_profile'):
                    instructor_id = user.instructor_profile.id
            except Exception as e:
                logger.error(f"Error getting instructor_id: {str(e)}")

            return Response({
                **tokens,
                'email': user.email,
                'username': user.username,
                'department': user.department,
                'api_key': user.api_key,
                'instructor_id': instructor_id
            })

        except ValueError:
            return Response({'error': 'Invalid token'}, status=status.HTTP_401_UNAUTHORIZED)
