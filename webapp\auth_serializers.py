from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from .models import Instructor

User = get_user_model()


class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.Char<PERSON>ield(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)
    department = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = User
        fields = ('email', 'username', 'password', 'password2', 'department')

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        validated_data.pop('password2')
        department = validated_data.pop('department', None)
        
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=validated_data['password'],
            department=department
        )
        
        # Create instructor profile
        Instructor.objects.create(
            user=user,
            name=user.username,
            department=department
        )
        
        return user


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'department', 'api_key')
        read_only_fields = ('id', 'api_key')


class GoogleAuthSerializer(serializers.Serializer):
    token = serializers.CharField(required=True)
