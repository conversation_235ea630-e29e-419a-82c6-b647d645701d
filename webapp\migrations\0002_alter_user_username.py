# Generated by Django 5.0.14 on 2025-04-22 10:25

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='username',
            field=models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits, spaces and @/./+/-/_ only.', max_length=150, validators=[django.core.validators.RegexValidator(code='invalid_username', message='Enter a valid username. This value may contain letters, numbers, spaces, and @/./+/-/_ characters.', regex='^[\\w\\s.@+-]+$')], verbose_name='username'),
        ),
    ]
