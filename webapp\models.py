from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
# Create your models here.

class user(AbstractUser):
    """
    Custom User model that extends Django's AbstractUser.
    This allows us to add custom fields to the User model.
    """
    # Override the username field to allow spaces
    username_validator = RegexValidator(
        regex=r'^[\w\s.@+-]+$',
        message=_('Enter a valid username. This value may contain letters, numbers, spaces, and @/./+/-/_ characters.'),
        code='invalid_username'
    )
    username = models.CharField(
        _('username'),
        max_length=150,
        unique=False,
        help_text=_('Required. 150 characters or fewer. Letters, digits, spaces and @/./+/-/_ only.'),
        validators=[username_validator],
        error_messages={
            'unique': _('A user with that username already exists.'),
        },
    )
    email = models.EmailField(_('email address'), unique=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    api_key = models.TextField(blank=True, null=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def save(self, *args, **kwargs):
        # First save the user
        super().save(*args, **kwargs)

        # Synchronize API key with Instructor model if it exists
        try:
            if hasattr(self, 'instructor_profile') and self.api_key:
                instructor = self.instructor_profile
                if instructor.api_key != self.api_key:
                    instructor.api_key = self.api_key
                    # Use update to avoid recursive save
                    Instructor.objects.filter(pk=instructor.pk).update(api_key=self.api_key)
        except Exception as e:
            # Log the error but don't prevent user from being saved
            print(f"Error synchronizing API key: {str(e)}")

    def __str__(self):
        return self.email


class Instructor(models.Model):
    user = models.OneToOneField(user, on_delete=models.CASCADE, related_name='instructor_profile')
    name = models.CharField(max_length=100)
    department = models.CharField(max_length=100, blank=True, null=True)
    api_key = models.TextField(blank=True, null=True)  # Keep API key in Instructor model for backward compatibility
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    def save(self, *args, **kwargs):
        # Synchronize API key with User model if it exists
        if self.user and self.api_key and self.user.api_key != self.api_key:
            self.user.api_key = self.api_key
            self.user.save(update_fields=['api_key'])
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class Course(models.Model):
    instructor = models.ForeignKey(Instructor, on_delete=models.PROTECT, related_name='courses')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    def __str__(self):
        return self.title


class Lesson(models.Model):
    course = models.ForeignKey(Course, on_delete=models.PROTECT, related_name='lessons')
    title = models.CharField(max_length=200)
    type = models.CharField(max_length=50, blank=True, null=True)
    file = models.URLField(max_length=500, blank=True, null=True, help_text="URL link to the lesson file")
    audio_transcript = models.TextField(blank=True, null=True, help_text="URL to audio transcript or the transcript text")
    summary = models.TextField(blank=True, null=True)
    docsource_id = models.CharField(max_length=100, blank=True, null=True, help_text="External document source identifier")
    video_id = models.CharField(max_length=500, blank=True, null=True, help_text="Video ID from Video Indexer")
    access_token = models.TextField(blank=True, null=True, help_text="Access token from Video Indexer")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    outcomes = models.ManyToManyField('Outcome', related_name='lessons', blank=True)

    def __str__(self):
        return self.title


class Outcome(models.Model):
    course = models.ForeignKey(Course, on_delete=models.PROTECT, related_name='outcomes')
    short_name = models.CharField(max_length=50, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.short_name or f"Outcome {self.id}"

        # fields = ['id', 'lesson', 'question_count', 'created_at', 'updated_at', 'answer_style', 'name']

class QuestionSet(models.Model):
    lesson = models.ForeignKey(Lesson, on_delete=models.PROTECT, related_name='question_sets')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    question_count = models.PositiveIntegerField(default=0)
    answer_style = models.CharField(max_length=50, blank=True, null=True)
    name = models.CharField(max_length=200, blank=True, null=True)

    def __str__(self):
        return f"QuestionSet {self.id} for Lesson: {self.lesson.title}"


class Question(models.Model):
    question_set = models.ForeignKey(QuestionSet, on_delete=models.PROTECT, related_name='questions')
    type = models.CharField(max_length=50, blank=True, null=True)
    text = models.TextField()
    answer = models.TextField(blank=True, null=True)
    #options = models.TextField(blank=True, null=True)
    blooms_taxonomy = models.CharField(max_length=50, blank=True, null=True)
    difficulty = models.CharField(max_length=50, blank=True, null=True)
    outcome = models.ForeignKey(Outcome, on_delete=models.PROTECT, null=True, blank=True, related_name='questions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.text[:50]
    
    @property
    def get_options(self):
        """Get all options for this question"""
        return self.options.all()
    
    @property
    def correct_option(self):
        """Get the correct option for this question"""
        return self.options.filter(is_correct=True).first()
    

class Option(models.Model):
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='options')
    text = models.TextField()
    is_correct = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.text[:50]
    
    class Meta:
        # Ensure only one correct answer per question (optional constraint)
        constraints = [
            models.UniqueConstraint(
                fields=['question'],
                condition=models.Q(is_correct=True),
                name='unique_correct_answer_per_question'
            )
        ]

# class McqQuestion(models.Model):
#     question_set = models.ForeignKey(QuestionSet, on_delete=models.PROTECT, related_name='questions')
#     type = models.CharField(max_length=50, blank=True, null=True)
#     text = models.TextField()
#     correct_answer = models.TextField(blank=True, null=True)
#     options = models.TextField(blank=True, null=True)
#     blooms_taxonomy = models.CharField(max_length=50, blank=True, null=True)
#     difficulty = models.CharField(max_length=50, blank=True, null=True)
#     outcome = models.ForeignKey(Outcome, on_delete=models.PROTECT, null=True, blank=True, related_name='questions')
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     def __str__(self):
#         return self.text[:50]

# class SentenceQuestion(models.Model):
#     question_set = models.ForeignKey(QuestionSet, on_delete=models.PROTECT, related_name='questions')
#     type = models.CharField(max_length=50, blank=True, null=True)
#     text = models.TextField()
#     correct_answer = models.TextField(blank=True, null=True)
#     options = models.JSONField(blank=True, null=True)
#     blooms_taxonomy = models.CharField(max_length=50, blank=True, null=True)
#     difficulty = models.CharField(max_length=50, blank=True, null=True)
#     outcome = models.ForeignKey(Outcome, on_delete=models.PROTECT, null=True, blank=True, related_name='questions')
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     def __str__(self):
#         return self.text[:50]
