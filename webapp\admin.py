from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import user, Instructor, Course

# Register custom User model
@admin.register(user)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'department', 'is_staff', 'is_active')
    search_fields = ('username', 'email', 'department')
    list_filter = ('is_staff', 'is_active', 'department')
    fieldsets = (
        (None, {'fields': ('username', 'email', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'department')}),
        ('API', {'fields': ('api_key',)}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'department'),
        }),
    )

# Register your models here.
@admin.register(Instructor)
class InstructorAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'department', 'created_at', 'updated_at')
    search_fields = ('name', 'department', 'user__email', 'user__username')
    list_filter = ('department', 'created_at', 'updated_at')

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('title', 'instructor', 'department', 'created_at', 'updated_at')
    search_fields = ('title', 'description', 'instructor__name')
    list_filter = ('department', 'created_at', 'updated_at', 'instructor')
    raw_id_fields = ('instructor',)
