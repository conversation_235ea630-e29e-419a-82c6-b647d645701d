from rest_framework import serializers
from django.db import transaction
from django.contrib.auth import get_user_model
from .models import Course, Outcome, Instructor, Lesson, QuestionSet, Question, Option

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'department']


class InstructorSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = Instructor
        fields = ['id', 'name', 'department', 'user', 'email', 'username', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at', 'user', 'email', 'username']


class OutcomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Outcome
        fields = ['id', 'short_name', 'description', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class OptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Option
        fields = ['id', 'text', 'is_correct', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class QuestionSerializer(serializers.ModelSerializer):
    options = OptionSerializer(many=True, read_only=True)
    correct_option = serializers.SerializerMethodField()
    
    class Meta:
        model = Question
        fields = ['id', 'question_set', 'type', 'text', 'answer', 'options',
                 'correct_option', 'blooms_taxonomy', 'difficulty', 'outcome', 
                 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_correct_option(self, obj):
        """Return the correct option for this question"""
        correct_opt = obj.options.filter(is_correct=True).first()
        if correct_opt:
            return OptionSerializer(correct_opt).data
        return None

# class McqQuestionSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = McqQuestion
#         fields = ['id', 'question_set', 'type', 'text', 'correct_answer', 'options', 'blooms_taxonomy', 'difficulty', 'outcome', 'created_at', 'updated_at']
#         read_only_fields = ['id', 'created_at', 'updated_at']

# class SentenceQuestionSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = SentenceQuestion
#         fields = ['id', 'question_set', 'type', 'text', 'correct_answer', 'blooms_taxonomy', 'difficulty', 'outcome', 'created_at', 'updated_at']
#         read_only_fields = ['id', 'created_at', 'updated_at']

class QuestionSerializerNew(serializers.ModelSerializer):
    # Add computed fields for better API response
    question_preview = serializers.SerializerMethodField()
    answer_preview = serializers.SerializerMethodField()
    
    class Meta:
        model = Question
        fields = [
            'id', 'question_set', 'type', 'text', 'blooms_taxonomy', 
            'difficulty', 'outcome', 'created_at', 'updated_at',
            'question_preview', 'answer_preview'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'question_preview', 'answer_preview']

    def get_question_preview(self, obj):
        """Extract just the question text without answers for preview"""
        if not obj.text:
            return ""
        
        # Split by common answer separators
        text = obj.text
        separators = ['\n\nAnswer:', '\n\nOptions:', '\n\nCorrect Answer:']
        
        for separator in separators:
            if separator in text:
                text = text.split(separator)[0]
                break
        
        # Limit to first 200 characters for preview
        return text[:200] + "..." if len(text) > 200 else text

    def get_answer_preview(self, obj):
        """Extract answer information for preview"""
        if not obj.text:
            return ""
        
        text = obj.text
        
        # Extract answer for essay/sentence questions
        if '\n\nAnswer:' in text:
            answer_part = text.split('\n\nAnswer:')[1]
            # Remove any additional sections that might come after
            for separator in ['\n\nOptions:', '\n\nCorrect Answer:']:
                if separator in answer_part:
                    answer_part = answer_part.split(separator)[0]
            return answer_part.strip()[:300] + "..." if len(answer_part.strip()) > 300 else answer_part.strip()
        
        # Extract correct answer for multiple choice questions
        elif '\n\nCorrect Answer:' in text:
            answer_part = text.split('\n\nCorrect Answer:')[1].strip()
            return answer_part[:200] + "..." if len(answer_part) > 200 else answer_part
        
        # For multiple choice, try to identify correct option
        elif '\n\nOptions:' in text and obj.type in ['multiple_choice', 'mcq']:
            return "Multiple choice question - see full text for options"
        
        return ""

    def to_representation(self, instance):
        """Customize the serialized representation"""
        data = super().to_representation(instance)
        
        # Add metadata about the question
        data['metadata'] = {
            'has_multiple_choice': 'Options:' in (instance.text or ''),
            'has_answer': any(separator in (instance.text or '') for separator in ['\n\nAnswer:', '\n\nCorrect Answer:']),
            'word_count': len((instance.text or '').split()),
            'character_count': len(instance.text or '')
        }
        
        return data

class QuestionSetSerializer(serializers.ModelSerializer):
    question_count = serializers.IntegerField(read_only=True)
    answer_style = serializers.CharField(read_only=True)
    name = serializers.CharField(read_only=True)

    class Meta:
        model = QuestionSet
        fields = ['id', 'lesson', 'question_count', 'created_at', 'updated_at', 'answer_style', 'name']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['question_count'] = instance.questions.count()
    
        # Get answer_style from the first question or default to "All"
        answer_style = "All"
        if instance.questions.exists():
            answer_style = instance.questions.first().type
        
        representation['answer_style'] = answer_style
        
        # Format date as DD-Mmm and create name
        date_str = instance.created_at.strftime('%d-%b')
        representation['name'] = f"{answer_style.title()} - {date_str}"
        
        return representation


class LessonSerializer(serializers.ModelSerializer):
    outcomes = OutcomeSerializer(many=True, read_only=True)
    question_sets = QuestionSetSerializer(many=True, read_only=True)
    outcome_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = Lesson
        fields = [
            'id', 
            'course', 
            'title', 
            'type', 
            'file', 
            'audio_transcript', 
            'summary', 
            'docsource_id',
            'video_id',
            'access_token',
            'created_at', 
            'updated_at',
            'outcomes',
            'outcome_ids',
            'question_sets'  # Add this field
        ]

    def create(self, validated_data):
        outcome_ids = validated_data.pop('outcome_ids', [])
        lesson = super().create(validated_data)
        if outcome_ids:
            lesson.outcomes.set(outcome_ids)
        return lesson

    def update(self, instance, validated_data):
        outcome_ids = validated_data.pop('outcome_ids', None)
        lesson = super().update(instance, validated_data)
        if outcome_ids is not None:
            lesson.outcomes.set(outcome_ids)
        return lesson


class CourseSerializer(serializers.ModelSerializer):
    instructor_name = serializers.ReadOnlyField(source='instructor.name')
    instructor_id = serializers.PrimaryKeyRelatedField(
        source='instructor',
        queryset=Instructor.objects.all()
    )
    outcomes = OutcomeSerializer(many=True, required=False)

    class Meta:
        model = Course
        fields = ['id', 'title', 'description', 'instructor_id', 'instructor_name',
                 'department', 'created_at', 'updated_at', 'outcomes']
        read_only_fields = ['id', 'created_at', 'updated_at']

    @transaction.atomic
    def create(self, validated_data):
        outcomes_data = validated_data.pop('outcomes', [])
        try:
            course = Course.objects.create(**validated_data)

            for outcome_data in outcomes_data:
                Outcome.objects.create(course=course, **outcome_data)

            return course
        except Exception as e:
            raise serializers.ValidationError(f"Failed to create course: {str(e)}")

    @transaction.atomic
    def update(self, instance, validated_data):
        outcomes_data = validated_data.pop('outcomes', [])
        try:
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            if outcomes_data is not None:
                instance.outcomes.all().delete()
                for outcome_data in outcomes_data:
                    Outcome.objects.create(course=instance, **outcome_data)

            return instance
        except Exception as e:
            raise serializers.ValidationError(f"Failed to update course: {str(e)}")



