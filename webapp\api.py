from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from .models import Instructor, Course, Lesson, Option, Outcome, QuestionSet, Question
from .serializers import InstructorSerializer, CourseSerializer, LessonSerializer, OutcomeSerializer, QuestionSetSerializer, QuestionSerializer
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from functools import wraps
import requests
from django.conf import settings
import os
import json


def require_api_key(view_method):
    """
    Decorator that checks if a valid API key is provided in the request headers.
    """
    @wraps(view_method)
    def wrapper(self, request, *args, **kwargs):
        # Get API key from header
        api_key = request.headers.get('api-key')

        # Check if API key is provided
        if not api_key:
            return Response(
                {"error": "API key is required"},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Check if API key is valid in User model
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            # First try to find the API key in the User model
            user = User.objects.get(api_key=api_key)
            instructor = user.instructor_profile
            # Attach both user and instructor to the request for use in the view
            request.user = user
            request.instructor = instructor
            return view_method(self, request, *args, **kwargs)
        except (User.DoesNotExist, AttributeError):
            # If not found in User model, try the old way with Instructor model
            try:
                instructor = Instructor.objects.get(api_key=api_key)
                # Attach the instructor to the request for use in the view
                request.instructor = instructor
                return view_method(self, request, *args, **kwargs)
            except Instructor.DoesNotExist:
                return Response(
                    {"error": "Invalid API key"},
                    status=status.HTTP_401_UNAUTHORIZED
                )

    return wrapper


class InstructorViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows instructors to be viewed or edited.
    """
    queryset = Instructor.objects.all()
    serializer_class = InstructorSerializer
    permission_classes = [permissions.AllowAny]  # Allow access without authentication

    def perform_create(self, serializer):
        serializer.save()

    def perform_update(self, serializer):
        serializer.save()

    @swagger_auto_schema(
        operation_description="List all instructors",
        responses={200: InstructorSerializer(many=True)},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )

    #@require_api_key
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Create a new instructor",
        request_body=InstructorSerializer,
        responses={201: InstructorSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    # @require_api_key
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Retrieve a specific instructor by ID",
        responses={200: InstructorSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    # @require_api_key
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Update an instructor",
        request_body=InstructorSerializer,
        responses={200: InstructorSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Delete an instructor",
        responses={204: "No content"},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class CourseViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows courses to be viewed or edited.
    """
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.AllowAny]  # Allow access without authentication

    def get_queryset(self):
        queryset = Course.objects.all()
        # Use the authenticated instructor from the request
        if hasattr(self.request, 'instructor') and self.request.instructor:
            queryset = queryset.filter(instructor=self.request.instructor)
        return queryset
    
    def perform_create(self, serializer):
        serializer.save()

    def perform_update(self, serializer):
        serializer.save()

    @swagger_auto_schema(
        operation_description="List all courses with their learning outcomes",
        responses={200: CourseSerializer(many=True)},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Create a new course with learning outcomes",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['title', 'description', 'department', 'instructor_id'],
            properties={
                'title': openapi.Schema(type=openapi.TYPE_STRING),
                'description': openapi.Schema(type=openapi.TYPE_STRING),
                'department': openapi.Schema(type=openapi.TYPE_STRING),
                'instructor_id': openapi.Schema(type=openapi.TYPE_INTEGER),
                'outcomes': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'short_name': openapi.Schema(type=openapi.TYPE_STRING),
                            'description': openapi.Schema(type=openapi.TYPE_STRING),
                        }
                    )
                ),
            }
        ),
        responses={
            201: CourseSerializer(),
            400: 'Bad Request',
            403: 'Forbidden'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def create(self, request, *args, **kwargs):
        # Use the authenticated instructor from the request
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(instructor=request.instructor)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @swagger_auto_schema(
        operation_description="Retrieve a specific course by ID",
        responses={200: CourseSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Update a course and its learning outcomes",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'title': openapi.Schema(type=openapi.TYPE_STRING),
                'description': openapi.Schema(type=openapi.TYPE_STRING),
                'department': openapi.Schema(type=openapi.TYPE_STRING),
                'instructor_id': openapi.Schema(type=openapi.TYPE_INTEGER),
                'outcomes': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'short_name': openapi.Schema(type=openapi.TYPE_STRING),
                            'description': openapi.Schema(type=openapi.TYPE_STRING),
                        }
                    )
                ),
            }
        ),
        responses={
            200: CourseSerializer(),
            400: 'Bad Request',
            403: 'Forbidden'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Delete a course",
        responses={204: "No content"},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Get courses by instructor ID",
        manual_parameters=[
            openapi.Parameter(
                'instructor_id',
                openapi.IN_QUERY,
                description="Filter courses by instructor ID",
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={200: CourseSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    @require_api_key
    def by_instructor(self, request):
        instructor_id = request.query_params.get('instructor_id')
        if instructor_id:
            courses = Course.objects.filter(instructor=instructor_id)
        else:
            courses = Course.objects.all()
        serializer = self.get_serializer(courses, many=True)
        return Response(serializer.data)


class LessonViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows lessons to be viewed or edited.
    """
    queryset = Lesson.objects.all()
    serializer_class = LessonSerializer
    permission_classes = [permissions.AllowAny]  # Allow access without authentication

    def perform_create(self, serializer):
        serializer.save()

    def perform_update(self, serializer):
        serializer.save()

    def get_queryset(self):
        return Lesson.objects.all().prefetch_related('outcomes')

    @swagger_auto_schema(
        operation_description="List all lessons",
        responses={200: LessonSerializer(many=True)},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    
    @require_api_key
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Create a new lesson",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['title', 'course'],
            properties={
                'title': openapi.Schema(type=openapi.TYPE_STRING),
                'course': openapi.Schema(type=openapi.TYPE_INTEGER),
                'type': openapi.Schema(type=openapi.TYPE_STRING),
                'file': openapi.Schema(type=openapi.TYPE_STRING),
                'audio_transcript': openapi.Schema(type=openapi.TYPE_STRING),
                'summary': openapi.Schema(type=openapi.TYPE_STRING),
                'docsource_id': openapi.Schema(type=openapi.TYPE_STRING, description="External document source identifier"),
                'outcome_ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_INTEGER)
                ),
            }
        ),
        responses={201: LessonSerializer},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def create(self, request, *args, **kwargs):
        if 'outcomes' in request.data:
            request.data['outcome_ids'] = request.data.pop('outcomes')
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Retrieve a specific lesson by ID",
        responses={200: LessonSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def retrieve(self, request, *args, **kwargs):
        # Get the lesson instance
        lesson = self.get_object()

        # Check if we need to check transcription status
        should_check_transcription = (
            (not lesson.audio_transcript or lesson.audio_transcript.strip() == '') and
            lesson.access_token and lesson.video_id
        )

        if should_check_transcription:
            # Get AI service URL from environment
            ai_service_url = os.getenv('AI_SERVICE_URL')
            if ai_service_url:
                # Prepare request to AI service
                endpoint = f"{ai_service_url.rstrip('/')}/api/transcribe/retrieve"
                headers = {
                    'Content-Type': 'application/json',
                    'api-key': request.headers.get('api-key')
                }
                payload = {
                    'video_id': lesson.video_id,
                    'access_token': lesson.access_token,
                    'video_url': lesson.file
                }

                try:
                    # Make request to AI service
                    response = requests.post(endpoint, json=payload, headers=headers)

                    if response.status_code == 200:
                        try:
                            ai_response = response.json()

                            # Check if transcription is complete
                            if ai_response.get('result') and ai_response['result'].strip():
                                # Update lesson with transcription results
                                lesson.audio_transcript = ai_response['result']
                                if ai_response.get('trans_result_id'):
                                    lesson.docsource_id = ai_response['trans_result_id']
                                # Clear transcription tokens
                                lesson.access_token = None
                                lesson.video_id = None
                                lesson.save()

                                # Return updated lesson with processing=false
                                serializer = self.get_serializer(lesson)
                                data = serializer.data
                                data['processing'] = False
                                return Response(data)
                            else:
                                # Transcription still in progress
                                serializer = self.get_serializer(lesson)
                                data = serializer.data
                                data['processing'] = True
                                return Response(data)

                        except ValueError:
                            # Invalid JSON response, treat as still processing
                            serializer = self.get_serializer(lesson)
                            data = serializer.data
                            data['processing'] = True
                            return Response(data)
                    else:
                        # API call failed, treat as still processing
                        serializer = self.get_serializer(lesson)
                        data = serializer.data
                        data['processing'] = True
                        return Response(data)

                except requests.RequestException:
                    # Network error, treat as still processing
                    serializer = self.get_serializer(lesson)
                    data = serializer.data
                    data['processing'] = True
                    return Response(data)
            else:
                # AI service URL not configured, treat as still processing
                serializer = self.get_serializer(lesson)
                data = serializer.data
                data['processing'] = True
                return Response(data)

        # Default case: return lesson as-is (no processing status needed)
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Update a lesson",
        request_body=LessonSerializer,
        properties={
                'course': openapi.Schema(type=openapi.TYPE_INTEGER),
                'title': openapi.Schema(type=openapi.TYPE_STRING),
                'type': openapi.Schema(type=openapi.TYPE_STRING),
                'file': openapi.Schema(type=openapi.TYPE_STRING),
                'audio_transcript': openapi.Schema(type=openapi.TYPE_STRING),
                'summary': openapi.Schema(type=openapi.TYPE_STRING),
                'docsource_id': openapi.Schema(type=openapi.TYPE_STRING),
                'outcome_ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_INTEGER)
                ),
            },
        responses={200: LessonSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def update(self, request, *args, **kwargs):
        if 'outcomes' in request.data:
            request.data['outcome_ids'] = request.data.pop('outcomes')
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Delete a lesson",
        responses={204: "No content"},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Get lessons by course ID",
        manual_parameters=[
            openapi.Parameter(
                'course_id',
                openapi.IN_QUERY,
                description="Filter lessons by course ID",
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={200: LessonSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    @require_api_key
    def by_course(self, request):
        course_id = request.query_params.get('course_id')
        if course_id:
            lessons = Lesson.objects.filter(course=course_id)
        else:
            lessons = Lesson.objects.all()
        serializer = self.get_serializer(lessons, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Initiate video transcription for a lesson",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['lesson_id'],
            properties={
                'lesson_id': openapi.Schema(type=openapi.TYPE_INTEGER, description="ID of the lesson to transcribe"),
            }
        ),
        responses={
            200: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(type=openapi.TYPE_STRING),
                    'lesson_id': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'video_id': openapi.Schema(type=openapi.TYPE_STRING),
                    'access_token': openapi.Schema(type=openapi.TYPE_STRING),
                }
            ),
            400: 'Bad Request',
            404: 'Lesson not found',
            500: 'Error initiating transcription'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @action(detail=False, methods=['post'], url_path='transcribe-video')
    @require_api_key
    def transcribe_video(self, request):
        # Get lesson_id from request
        lesson_id = request.data.get('lesson_id')

        # Validate lesson_id
        if not lesson_id:
            return Response(
                {"error": "Missing required parameter: lesson_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            lesson_id = int(lesson_id)
        except ValueError:
            return Response(
                {"error": "lesson_id must be an integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the lesson
        try:
            lesson = Lesson.objects.get(id=lesson_id)
        except Lesson.DoesNotExist:
            return Response(
                {"error": f"Lesson with ID {lesson_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if lesson has a file URL
        if not lesson.file:
            return Response(
                {"error": f"Lesson with ID {lesson_id} does not have a file URL"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get AI service URL from environment
        ai_service_url = os.getenv('AI_SERVICE_URL')
        if not ai_service_url:
            return Response(
                {"error": "AI service URL not configured"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Prepare request to AI service
        endpoint = f"{ai_service_url}api/transcribe/create"
        headers = {
            'Content-Type': 'application/json',
            'api-key': request.headers.get('api-key')
        }
        payload = {
            'video_url': lesson.file
        }

        try:
            # Make request to AI service
            response = requests.post(endpoint, json=payload, headers=headers)

            # Check if the response status code indicates an error
            if response.status_code >= 400:
                return Response(
                    {
                        "error": f"AI service returned error status: {response.status_code}",
                        "details": response.text[:500]
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Try to parse as JSON
            try:
                ai_response = response.json()
            except ValueError:
                return Response(
                    {"error": "AI service returned invalid JSON", "response": response.text[:500]},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Extract video_id and access_token from response
            video_id = ai_response.get('video_id')
            access_token = ai_response.get('access_token')

            if not video_id or not access_token:
                return Response(
                    {"error": "AI service response missing video_id or access_token", "response": ai_response},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Update lesson with video_id and access_token
            lesson.video_id = video_id
            lesson.access_token = access_token
            lesson.save()

            return Response({
                "message": "Video transcription initiated successfully",
                "lesson_id": lesson_id,
                "video_id": video_id,
                "access_token": access_token
            }, status=status.HTTP_200_OK)

        except requests.RequestException as e:
            return Response(
                {
                    "error": f"Error calling AI service: {str(e)}",
                    "request_payload": payload,
                    "endpoint": endpoint
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response(
                {"error": f"Error initiating video transcription: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @swagger_auto_schema(
        operation_description="Summarize video content for a lesson",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['lesson_id'],
            properties={
                'lesson_id': openapi.Schema(type=openapi.TYPE_INTEGER, description="ID of the lesson to summarize"),
            }
        ),
        responses={
            200: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(type=openapi.TYPE_STRING),
                    'lesson_id': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'summary': openapi.Schema(type=openapi.TYPE_STRING),
                    'result_url': openapi.Schema(type=openapi.TYPE_STRING),
                }
            ),
            400: 'Bad Request',
            404: 'Lesson not found',
            500: 'Error generating summary'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @action(detail=False, methods=['post'], url_path='summarize-video')
    @require_api_key
    def summarize_video(self, request):
        # Get lesson_id from request
        lesson_id = request.data.get('lesson_id')

        # Validate lesson_id
        if not lesson_id:
            return Response(
                {"error": "Missing required parameter: lesson_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            lesson_id = int(lesson_id)
        except ValueError:
            return Response(
                {"error": "lesson_id must be an integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the lesson
        try:
            lesson = Lesson.objects.get(id=lesson_id)
        except Lesson.DoesNotExist:
            return Response(
                {"error": f"Lesson with ID {lesson_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if lesson has audio_transcript and docsource_id
        if not lesson.audio_transcript or not lesson.docsource_id:
            return Response(
                {"error": f"Lesson with ID {lesson_id} does not have audio_transcript or docsource_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get AI service URL from environment
        ai_service_url = os.getenv('AI_SERVICE_URL')
        if not ai_service_url:
            return Response(
                {"error": "AI service URL not configured"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Prepare request to AI service
        endpoint = f"{ai_service_url.rstrip('/')}/api/summarize/"
        headers = {
            'Content-Type': 'application/json',
            'api-key': request.headers.get('api-key')
        }
        payload = {
            'text': lesson.audio_transcript,
            'trans_result_id': lesson.docsource_id
        }

        try:
            # Make request to AI service
            response = requests.post(endpoint, json=payload, headers=headers)

            # Check if the response status code indicates an error
            if response.status_code >= 400:
                return Response(
                    {
                        "error": f"AI service returned error status: {response.status_code}",
                        "details": response.text[:500]
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Try to parse as JSON
            try:
                ai_response = response.json()
            except ValueError:
                return Response(
                    {"error": "AI service returned invalid JSON", "response": response.text[:500]},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Update lesson with summary
            if ai_response.get('summary'):
                lesson.summary = ai_response['summary']
                lesson.save()

            return Response({
                "message": "Video summary generated successfully",
                "lesson_id": lesson.id,
                "summary": ai_response.get('summary'),
                "result_url": ai_response.get('result_url')
            })

        except requests.RequestException as e:
            return Response(
                {"error": f"Failed to connect to AI service: {str(e)}"},
                status=status.HTTP_502_BAD_GATEWAY
            )


class OutcomeViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows learning outcomes to be viewed or edited.
    """
    queryset = Outcome.objects.all()
    serializer_class = OutcomeSerializer
    permission_classes = [permissions.AllowAny]  # Allow access without authentication

    def perform_create(self, serializer):
        serializer.save()

    def perform_update(self, serializer):
        serializer.save()

    @swagger_auto_schema(
        operation_description="List all learning outcomes",
        responses={200: OutcomeSerializer(many=True)},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Create a new learning outcome",
        request_body=OutcomeSerializer,
        responses={201: OutcomeSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Retrieve a specific learning outcome by ID",
        responses={200: OutcomeSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Update a learning outcome",
        request_body=OutcomeSerializer,
        responses={200: OutcomeSerializer()},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Delete a learning outcome",
        responses={204: "No content"},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Get learning outcomes by course ID",
        manual_parameters=[
            openapi.Parameter(
                'course_id',
                openapi.IN_QUERY,
                description="Filter learning outcomes by course ID",
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={200: OutcomeSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    @require_api_key
    def by_course(self, request):
        course_id = request.query_params.get('course_id')
        if course_id:
            outcomes = Outcome.objects.filter(course=course_id)
        else:
            outcomes = Outcome.objects.all()
        serializer = self.get_serializer(outcomes, many=True)
        return Response(serializer.data)


class QuestionSetViewSet(viewsets.ModelViewSet):
    queryset = QuestionSet.objects.all()
    serializer_class = QuestionSetSerializer
    permission_classes = [permissions.AllowAny]  # Allow access without authentication

    @swagger_auto_schema(
        operation_description="List all question sets",
        responses={200: QuestionSetSerializer(many=True)},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Create a new question set",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['lesson'],
            properties={
                'lesson': openapi.Schema(type=openapi.TYPE_INTEGER),
            }
        ),
        responses={
            201: QuestionSetSerializer,
            400: 'Bad Request'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Retrieve a specific question set by ID",
        responses={200: QuestionSetSerializer},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Update a question set",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'lesson': openapi.Schema(type=openapi.TYPE_INTEGER),
            }
        ),
        responses={
            200: QuestionSetSerializer,
            400: 'Bad Request',
            404: 'Not Found'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Delete a question set",
        responses={
            204: 'No Content',
            404: 'Not Found'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Generate a new question set for a lesson",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['lesson_id', 'answer_style', 'n'],
            properties={
                'lesson_id': openapi.Schema(type=openapi.TYPE_INTEGER, description="ID of the lesson"),
                'answer_style': openapi.Schema(type=openapi.TYPE_STRING, description="Question style: mcq, sentence, or all"),
                'n': openapi.Schema(type=openapi.TYPE_INTEGER, description="Number of questions to generate"),
            }
        ),
        responses={
            201: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'question_set': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'questions': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    'count': openapi.Schema(type=openapi.TYPE_INTEGER)
                }
            ),
            400: 'Bad Request',
            404: 'Lesson not found',
            500: 'Error generating questions'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )

    @action(detail=False, methods=['post'])
    @require_api_key  # This decorator should handle the API key authentication
    def generate(self, request):
        # Get parameters from request
        lesson_id = request.data.get('lesson_id')
        answer_style = request.data.get('answer_style')
        n = request.data.get('n')

        # Validate parameters
        if not all([lesson_id, answer_style, n]):
            return Response(
                {"error": "Missing required parameters: lesson_id, answer_style, n"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Map our answer_style values to what the API expects
        answer_style_mapping = {
            'multiple_choice': 'multiple_choice',
            'mcq': 'multiple_choice',
            'sentences': 'sentences',
            'sentence': 'sentences',
            'all': 'all'
        }

        if answer_style not in answer_style_mapping:
            return Response(
                {"error": f"answer_style must be one of: {', '.join(answer_style_mapping.keys())}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Convert our answer_style to what the API expects
        api_answer_style = answer_style_mapping[answer_style]

        try:
            n = int(n)
            if n <= 0:
                raise ValueError("n must be positive")
        except ValueError:
            return Response(
                {"error": "n must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the lesson
        try:
            lesson = Lesson.objects.get(id=lesson_id)
        except Lesson.DoesNotExist:
            return Response(
                {"error": f"Lesson with ID {lesson_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if lesson has docsource_id
        if not lesson.docsource_id:
            return Response(
                {"error": f"Lesson with ID {lesson_id} does not have a docsource_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if lesson title contains "_direct"
        # if lesson.title and "_direct" in lesson.title:
        return self._process_qa_generation_request(lesson_id, answer_style, n, request)

        # Get AI service URL from environment
        # ai_service_url = os.getenv('AI_SERVICE_URL')
        # if not ai_service_url:
        #     return Response(
        #         {"error": "AI service URL not configured"},
        #         status=status.HTTP_500_INTERNAL_SERVER_ERROR
        #     )

        # # Prepare request to AI service
        # endpoint = f"{ai_service_url.rstrip('/')}/api/qa_generation/"
        # headers = {
        #     'Content-Type': 'application/json',
        #     'api-key': request.headers.get('api-key')
        # }
        # payload = {
        #     'answer_style': api_answer_style,
        #     'n': n,
        #     'docsource_id': lesson.docsource_id
        # }

        # try:
        #     # Make request to AI service
        #     response = requests.post(endpoint, json=payload, headers=headers)

        #     # Check if the response status code indicates an error
        #     if response.status_code >= 400:
        #         return Response(
        #             {
        #                 "error": f"AI service returned error status: {response.status_code}",
        #                 "details": response.text[:500]
        #             },
        #             status=status.HTTP_502_BAD_GATEWAY
        #         )

        #     # Try to parse as JSON if possible
        #     try:
        #         ai_response = response.json()
        #     except ValueError:
        #         return Response(
        #             {"error": "AI service returned invalid JSON", "response": response.text[:500]},
        #             status=status.HTTP_502_BAD_GATEWAY
        #         )

        #     # Check if the response has the expected structure
        #     if not ai_response.get('result') or not ai_response['result'].get('questions'):
        #         return Response(
        #             {"error": "AI service response does not contain questions", "response": ai_response},
        #             status=status.HTTP_502_BAD_GATEWAY
        #         )

        #     # Create a new QuestionSet
        #     question_set = QuestionSet.objects.create(lesson=lesson)

        #     # Create Question objects for each question in the response
        #     questions = []

        #     # Process the questions array - handle both list and dict formats
        #     for q_item in ai_response['result']['questions']:
        #         # Skip empty items
        #         if not q_item:
        #             continue

        #         # Handle different response formats
        #         if isinstance(q_item, list):
        #             # It's a list of questions
        #             for q_subitem in q_item:
        #                 if not q_subitem:
        #                     continue

        #                 # If it's a dictionary with question/answer
        #                 if isinstance(q_subitem, dict):
        #                     if 'question' in q_subitem:
        #                         question_text = q_subitem.get('question', '')
        #                         if 'answer' in q_subitem:
        #                             # Handle different answer formats
        #                             answer = q_subitem['answer']
        #                             if isinstance(answer, list):
        #                                 # Format multiple choice answers
        #                                 answer_text = "\n\nAnswer options:\n"
        #                                 for idx, option in enumerate(answer):
        #                                     if isinstance(option, dict):
        #                                         answer_text += f"{idx+1}. {option.get('answer', '')}"
        #                                         if option.get('correct', False):
        #                                             answer_text += " (Correct)"
        #                                         answer_text += "\n"
        #                                     else:
        #                                         answer_text += f"{idx+1}. {option}\n"
        #                                 question_text += answer_text
        #                             else:
        #                                 # Simple string answer
        #                                 question_text += f"\n\nAnswer: {answer}"

        #                         # Create the question
        #                         question = Question.objects.create(
        #                             question_set=question_set,
        #                             text=question_text,
        #                             type=answer_style,
        #                             blooms_taxonomy='remember',
        #                             difficulty='medium'
        #                         )
        #                         questions.append(question)
        #                 elif isinstance(q_subitem, str):
        #                     # It's a simple string question
        #                     question = Question.objects.create(
        #                         question_set=question_set,
        #                         text=q_subitem,
        #                         type=answer_style,
        #                         blooms_taxonomy='remember',
        #                         difficulty='medium'
        #                     )
        #                     questions.append(question)
        #         elif isinstance(q_item, dict):
        #             # It's a dictionary with question/answer
        #             if 'question' in q_item:
        #                 question_text = q_item.get('question', '')
        #                 if 'answer' in q_item:
        #                     # Handle different answer formats
        #                     answer = q_item['answer']
        #                     if isinstance(answer, list):
        #                         # Format multiple choice answers
        #                         answer_text = "\n\nAnswer options:\n"
        #                         for idx, option in enumerate(answer):
        #                             if isinstance(option, dict):
        #                                 answer_text += f"{idx+1}. {option.get('answer', '')}"
        #                                 if option.get('correct', False):
        #                                     answer_text += " (Correct)"
        #                                 answer_text += "\n"
        #                             else:
        #                                 answer_text += f"{idx+1}. {option}\n"
        #                         question_text += answer_text
        #                     else:
        #                         # Simple string answer
        #                         question_text += f"\n\nAnswer: {answer}"

        #                 # Create the question
        #                 question = Question.objects.create(
        #                     question_set=question_set,
        #                     text=question_text,
        #                     type=answer_style,
        #                     blooms_taxonomy='remember',
        #                     difficulty='medium'
        #                 )
        #                 questions.append(question)

        #     # If no questions were created, return an error
        #     if not questions:
        #         # Delete the empty question set
        #         question_set.delete()
        #         return Response(
        #             {"error": "No valid questions could be extracted from the AI service response"},
        #             status=status.HTTP_502_BAD_GATEWAY
        #         )

        #     # Return the created QuestionSet and Questions
        #     from .serializers import QuestionSerializer

        #     return Response({
        #         "question_set": self.get_serializer(question_set).data,
        #         "questions": QuestionSerializer(questions, many=True).data,
        #         "count": len(questions)
        #     }, status=status.HTTP_201_CREATED)

        # except requests.RequestException as e:
        #     return Response(
        #         {
        #             "error": f"Error calling AI service: {str(e)}",
        #             "request_payload": payload,
        #             "endpoint": endpoint
        #         },
        #         status=status.HTTP_500_INTERNAL_SERVER_ERROR
        #     )
        # except Exception as e:
        #     import traceback
        #     traceback.print_exc()
        #     return Response(
        #         {"error": f"Error generating questions: {str(e)}"},
        #         status=status.HTTP_500_INTERNAL_SERVER_ERROR
        #     )
        
    def _process_qa_generation_request(self, lesson_id, answer_style, n, request):
        """
        Helper method to process question generation requests.
        
        Args:
            lesson_id: ID of the lesson
            answer_style: Style of answers (mcq, sentence, etc.)
            n: Number of questions to generate
            request: The original request object
            
        Returns:
            Response object with generated questions or error
        """
        # Validate parameters
        if not all([lesson_id, answer_style, n]):
            return Response(
                {"error": "Missing required parameters: lesson_id, answer_style, n"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Map our answer_style values to what the API expects
        answer_style_mapping = {
            'multiple_choice': 'multiple_choice',
            'mcq': 'multiple_choice',
            'sentences': 'sentences',
            'sentence': 'sentences',
            'all': 'all'
        }

        if answer_style not in answer_style_mapping:
            return Response(
                {"error": f"answer_style must be one of: {', '.join(answer_style_mapping.keys())}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Convert our answer_style to what the API expects
        api_answer_style = answer_style_mapping[answer_style]

        try:
            n = int(n)
            if n <= 0:
                raise ValueError("n must be positive")
        except ValueError:
            return Response(
                {"error": "n must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the lesson
        try:
            lesson = Lesson.objects.get(id=lesson_id)
            lessonOutcomes = lesson.course.outcomes.all()
        except Lesson.DoesNotExist:
            return Response(
                {"error": f"Lesson with ID {lesson_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if lesson has docsource_id
        if not lesson.docsource_id:
            return Response(
                {"error": f"Lesson with ID {lesson_id} does not have a docsource_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get AI service URL from environment
        ai_service_url = os.getenv('AI_SERVICE_URL')
        if not ai_service_url:
            return Response(
                {"error": "AI service URL not configured"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Prepare request to AI service
        endpoint = f"{ai_service_url.rstrip('/')}/api/qa_generation_multi/"
        headers = {
            'Content-Type': 'application/json',
            'api-key': request.headers.get('api-key')
        }
        payload = {
            'answer_style': api_answer_style,
            'n': n,
            'docsource_id': [lesson.docsource_id],
            'lesson_id' : lesson.id,
            'outcomes': [o.description for o in lessonOutcomes]
        }

        try:
            # Make request to AI service
            response = requests.post(endpoint, json=payload, headers=headers)

            # Check if the response status code indicates an error
            if response.status_code >= 400:
                return Response(
                    {
                        "error": f"AI service returned error status: {response.status_code}",
                        "details": response.text[:500]
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Try to parse as JSON if possible
            try:
                ai_response = response.json()
            except ValueError:
                return Response(
                    {"error": "AI service returned invalid JSON", "response": response.text[:500]},
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            print(json.dumps(ai_response))

            # Validate response structure
            if not isinstance(ai_response, dict) or not ai_response.get('result'):
                return Response(
                    {"error": "AI service response missing 'result' field", "response": ai_response},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            result = ai_response['result']
            if not isinstance(result, dict) or not result.get('questions'):
                return Response(
                    {"error": "AI service response missing 'questions' field in result", "response": ai_response},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            questions_data = result['questions']
            if not isinstance(questions_data, list):
                return Response(
                    {"error": "AI service response 'questions' field is not a list", "response": ai_response},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Create a new QuestionSet
            question_set = QuestionSet.objects.create(lesson=lesson)

            # Create Question objects for each question in the response
            questions = []

            for q_item in questions_data:
                if not q_item or not isinstance(q_item, dict):
                    continue

                try:
                    # Extract question data based on the new format
                    question_text = q_item.get('questionText', '')
                    if not question_text:
                        continue

                    # Extract additional fields
                    bloom_level = q_item.get('bloomLevel', '')
                    difficulty_level = q_item.get('difficultyLevel', '')
                    correct_answer = q_item.get('correctAnswer', '')
                    courseOutcome = q_item.get('courseOutcome', '')

                    # Map bloom level to our taxonomy
                    bloom_mapping = {
                        'Remember': 'remember',
                        'Remembering': 'remember',
                        'Understand': 'understand',
                        'Understanding': 'understand',
                        'Apply': 'apply',
                        'Applying': 'apply',
                        'Analyze': 'analyze',
                        'Analyzing': 'analyze',
                        'Evaluate': 'evaluate',
                        'Evaluating': 'evaluate',
                        'Create': 'create',
                        'Creating': 'create'
                    }
                    blooms_taxonomy = bloom_mapping.get(bloom_level, '')

                    # Map difficulty level
                    difficulty_mapping = {
                        'Easy': 'easy',
                        'Medium': 'medium',
                        'Hard': 'hard'
                    }
                    difficulty = difficulty_mapping.get(difficulty_level, '')
                    Outcome = lessonOutcomes.get(description=courseOutcome)

                    # Create the question
                    
                    question = Question.objects.create(
                        question_set=question_set,
                        text=question_text,
                        type=answer_style,  # Use the original answer_style parameter
                        blooms_taxonomy=blooms_taxonomy,
                        difficulty=difficulty,
                        outcome=Outcome
                    )

                    # Handle multiple choice questions
                    if api_answer_style == 'multiple_choice' and 'options' in q_item:
                        options = q_item.get('options', [])
                        # if isinstance(options, list) and options:
                        #     # Format the question text with options
                        #     formatted_text = question_text + "\n\nOptions:\n"
                        #     for idx, option in enumerate(options, 1):
                        #         formatted_text += f"{idx}. {option}\n"
                            
                        #     if correct_answer:
                        #         formatted_text += f"\nCorrect Answer: {correct_answer}"
                            
                        #     question_text = formatted_text
                        if isinstance(options, list) and options:
                            option_objects = []
                            
                            for option_text in options:
                                is_correct = (option_text == correct_answer)
                                
                                option_obj = Option.objects.create(
                                    question=question,
                                    text=option_text,
                                    is_correct=is_correct
                                )
                                
                                option_objects.append(option_obj)
                            
                            #question.save()

                    # Handle essay/sentence questions
                    elif api_answer_style == 'sentences':
                        if correct_answer:
                            question.answer = correct_answer
                            # question.save()

                    questions.append(question)

                except Exception as e:
                    # Log the error but continue processing other questions
                    print(f"Error processing question item: {e}")
                    print(f"Question item: {q_item}")
                    continue

            # If no questions were created, return an error
            if not questions:
                # Delete the empty question set
                question_set.delete()
                return Response(
                    {"error": "No valid questions could be extracted from the AI service response"},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Return the created QuestionSet and Questions
            from .serializers import QuestionSerializer

            if api_answer_style in ['multiple_choice', 'sentences']:
                # Prefetch the options for all questions to include them in the response
                questions_with_options = Question.objects.filter(
                    id__in=[q.id for q in questions]
                ).prefetch_related('options')
                
                return Response({
                    "question_set": self.get_serializer(question_set).data,
                    "questions": QuestionSerializer(questions_with_options, many=True).data,
                    "count": len(questions)
                }, status=status.HTTP_201_CREATED)
            else:
                return Response(
                    {"error": "Illegal question type"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except requests.RequestException as e:
            return Response(
                {
                    "error": f"Error calling AI service: {str(e)}",
                    "request_payload": payload,
                    "endpoint": endpoint
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response(
                {"error": f"Error generating questions: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    @require_api_key  # This decorator should handle the API key authentication
    def generate_direct(self, request):
        # Get parameters from request
        lesson_id = request.data.get('lesson_id')
        answer_style = request.data.get('answer_style')
        n = request.data.get('n')

        return self._process_qa_generation_request(lesson_id, answer_style, n, request)


    @action(detail=False, methods=['post'])
    @require_api_key  # This decorator should handle the API key authentication
    def generate_direct_old(self, request):
        # Get parameters from request
        lesson_id = request.data.get('lesson_id')
        answer_style = request.data.get('answer_style')
        n = request.data.get('n')

        # Validate parameters
        if not all([lesson_id, answer_style, n]):
            return Response(
                {"error": "Missing required parameters: lesson_id, answer_style, n"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Map our answer_style values to what the API expects
        answer_style_mapping = {
            'multiple_choice': 'multiple_choice',
            'mcq': 'multiple_choice',
            'sentences': 'sentences',
            'sentence': 'sentences',
            'all': 'all'
        }

        if answer_style not in answer_style_mapping:
            return Response(
                {"error": f"answer_style must be one of: {', '.join(answer_style_mapping.keys())}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Convert our answer_style to what the API expects
        api_answer_style = answer_style_mapping[answer_style]

        try:
            n = int(n)
            if n <= 0:
                raise ValueError("n must be positive")
        except ValueError:
            return Response(
                {"error": "n must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the lesson
        try:
            lesson = Lesson.objects.get(id=lesson_id)
        except Lesson.DoesNotExist:
            return Response(
                {"error": f"Lesson with ID {lesson_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if lesson has docsource_id
        if not lesson.docsource_id:
            return Response(
                {"error": f"Lesson with ID {lesson_id} does not have a docsource_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get AI service URL from environment
        ai_service_url = os.getenv('AI_SERVICE_URL')
        if not ai_service_url:
            return Response(
                {"error": "AI service URL not configured"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Prepare request to AI service
        endpoint = f"{ai_service_url.rstrip('/')}/api/qa_generation_dir/"
        headers = {
            'Content-Type': 'application/json',
            'api-key': request.headers.get('api-key')
        }
        payload = {
            'answer_style': api_answer_style,
            'n': n,
            'docsource_id': lesson.docsource_id
        }

        try:
            # Make request to AI service
            response = requests.post(endpoint, json=payload, headers=headers)

            # Check if the response status code indicates an error
            if response.status_code >= 400:
                return Response(
                    {
                        "error": f"AI service returned error status: {response.status_code}",
                        "details": response.text[:500]
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Try to parse as JSON if possible
            try:
                ai_response = response.json()
            except ValueError:
                return Response(
                    {"error": "AI service returned invalid JSON", "response": response.text[:500]},
                    status=status.HTTP_502_BAD_GATEWAY
                )
            print(json.dumps(ai_response))

            # Check if the response has the expected structure
            if not ai_response.get('result') or not ai_response['result'].get('questions'):
                return Response(
                    {"error": "AI service response does not contain questions", "response": ai_response},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Create a new QuestionSet
            question_set = QuestionSet.objects.create(lesson=lesson)

            # Create Question objects for each question in the response
            questions = []

            # Process the questions array - handle both list and dict formats
            for q_item in ai_response['result']['questions']:
                # Skip empty items
                if not q_item:
                    continue

                # Handle different response formats
                if isinstance(q_item, list):
                    # It's a list of questions
                    for q_subitem in q_item:
                        if not q_subitem:
                            continue

                        # If it's a dictionary with question/answer
                        if isinstance(q_subitem, dict):
                            if 'question' in q_subitem:
                                question_text = q_subitem.get('question', '')
                                if 'answer' in q_subitem:
                                    # Handle different answer formats
                                    answer = q_subitem['answer']
                                    if isinstance(answer, list):
                                        # Format multiple choice answers
                                        answer_text = "\n\nAnswer options:\n"
                                        for idx, option in enumerate(answer):
                                            if isinstance(option, dict):
                                                answer_text += f"{idx+1}. {option.get('answer', '')}"
                                                if option.get('correct', False):
                                                    answer_text += " (Correct)"
                                                answer_text += "\n"
                                            else:
                                                answer_text += f"{idx+1}. {option}\n"
                                        question_text += answer_text
                                    else:
                                        # Simple string answer
                                        question_text += f"\n\nAnswer: {answer}"

                                # Create the question
                                question = Question.objects.create(
                                    question_set=question_set,
                                    text=question_text,
                                    type=answer_style,
                                    blooms_taxonomy='remember',
                                    difficulty='medium'
                                )
                                questions.append(question)
                        elif isinstance(q_subitem, str):
                            # It's a simple string question
                            question = Question.objects.create(
                                question_set=question_set,
                                text=q_subitem,
                                type=answer_style,
                                blooms_taxonomy='remember',
                                difficulty='medium'
                            )
                            questions.append(question)
                elif isinstance(q_item, dict):
                    # It's a dictionary with question/answer
                    if 'question' in q_item:
                        question_text = q_item.get('question', '')
                        if 'answer' in q_item:
                            # Handle different answer formats
                            answer = q_item['answer']
                            if isinstance(answer, list):
                                # Format multiple choice answers
                                answer_text = "\n\nAnswer options:\n"
                                for idx, option in enumerate(answer):
                                    if isinstance(option, dict):
                                        answer_text += f"{idx+1}. {option.get('answer', '')}"
                                        if option.get('correct', False):
                                            answer_text += " (Correct)"
                                        answer_text += "\n"
                                    else:
                                        answer_text += f"{idx+1}. {option}\n"
                                question_text += answer_text
                            else:
                                # Simple string answer
                                question_text += f"\n\nAnswer: {answer}"

                        # Create the question
                        question = Question.objects.create(
                            question_set=question_set,
                            text=question_text,
                            type=answer_style,
                            blooms_taxonomy='remember',
                            difficulty='medium'
                        )
                        questions.append(question)

            # If no questions were created, return an error
            if not questions:
                # Delete the empty question set
                question_set.delete()
                return Response(
                    {"error": "No valid questions could be extracted from the AI service response"},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Return the created QuestionSet and Questions
            from .serializers import QuestionSerializer

            return Response({
                "question_set": self.get_serializer(question_set).data,
                "questions": QuestionSerializer(questions, many=True).data,
                "count": len(questions)
            }, status=status.HTTP_201_CREATED)

        except requests.RequestException as e:
            return Response(
                {
                    "error": f"Error calling AI service: {str(e)}",
                    "request_payload": payload,
                    "endpoint": endpoint
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response(
                {"error": f"Error generating questions: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


    @swagger_auto_schema(
        operation_description="Generate a Bloom's taxonomy question set for a lesson",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['lesson_id', 'answer_style', 'n'],
            properties={
                'lesson_id': openapi.Schema(type=openapi.TYPE_INTEGER, description="ID of the lesson"),
                'answer_style': openapi.Schema(type=openapi.TYPE_STRING, description="Question style: mcq, sentence, or all"),
                'n': openapi.Schema(type=openapi.TYPE_INTEGER, description="Number of questions to generate"),
                'taxonomy_levels': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="Bloom's taxonomy levels to include (e.g., ['remember', 'understand', 'apply'])"
                )
            }
        ),
        responses={
            201: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'question_set': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'questions': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    'count': openapi.Schema(type=openapi.TYPE_INTEGER)
                }
            ),
            400: 'Bad Request',
            404: 'Lesson not found',
            500: 'Error generating questions'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @action(detail=False, methods=['post'])
    @require_api_key
    def generate_blooms(self, request):
        # Get parameters from request
        lesson_id = request.data.get('lesson_id')
        answer_style = request.data.get('answer_style')
        n = request.data.get('n')
        taxonomy_levels = request.data.get('taxonomy_levels', [])

        # print(f"Received request for Bloom's taxonomy questions: lesson_id={lesson_id}, answer_style={answer_style}, n={n}, taxonomy_levels={taxonomy_levels}")
        # Validate parameters
        if not all([lesson_id, answer_style, n]):
            return Response(
                {"error": "Missing required parameters: lesson_id, answer_style, n"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Map our answer_style values to what the API expects
        answer_style_mapping = {
            'multiple_choice': 'multiple_choice',
            'mcq': 'multiple_choice',
            'sentences': 'sentences',
            'sentence': 'sentences',
            'all': 'all'
        }

        if answer_style not in answer_style_mapping:
            return Response(
                {"error": "answer_style must be one of: mcq, sentence, all"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Convert our answer_style to what the API expects
        api_answer_style = answer_style_mapping[answer_style]

        try:
            n = int(n)
            if n <= 0:
                raise ValueError("n must be positive")
        except ValueError:
            return Response(
                {"error": "n must be a positive integer"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the lesson
        try:
            lesson = Lesson.objects.get(id=lesson_id)
            # print(f"Found lesson: {lesson.title} (ID: {lesson.id}), docsource_id: {lesson.docsource_id}")
        except Lesson.DoesNotExist:
            return Response(
                {"error": f"Lesson with ID {lesson_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if lesson has docsource_id
        if not lesson.docsource_id:
            return Response(
                {"error": f"Lesson with ID {lesson_id} does not have a docsource_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get AI service URL from environment
        ai_service_url = os.getenv('AI_SERVICE_URL')
        if not ai_service_url:
            return Response(
                {"error": "AI service URL not configured"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Prepare request to AI service
        endpoint = f"{ai_service_url.rstrip('/')}/api/bloomtax_generation/"
        headers = {
            'Content-Type': 'application/json',
            'api-key': request.headers.get('api-key')
        }

        # Valid cognitive levels
        valid_cognitive_levels = ['remember', 'analyze', 'create', 'evaluate', 'understand', 'apply', 'all']

        # If taxonomy_levels is empty or contains 'all', use 'all'
        cognitive_level = 'all'
        if taxonomy_levels and 'all' not in taxonomy_levels:
            # Use the first taxonomy level provided if it's valid
            if taxonomy_levels[0] in valid_cognitive_levels:
                cognitive_level = taxonomy_levels[0]
            else:
                return Response(
                    {"error": f"Invalid taxonomy level: {taxonomy_levels[0]}. Must be one of: {', '.join(valid_cognitive_levels)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        payload = {
            'answer_style': api_answer_style,
            'cognitive_level': cognitive_level,
            'n': n,
            'docsource_id': lesson.docsource_id
        }


        try:
            # Make request to AI service
            response = requests.post(endpoint, json=payload, headers=headers)
            print(f"AI service response status: {response.status_code}")

            # Try to parse as JSON if possible
            try:
                ai_response = response.json()
                # print(f"AI service response: {ai_response}")
                # print(f"AI response structure: {list(ai_response.keys())}")
            except ValueError:
                print(f"Response is not valid JSON: {response.text[:200]}")
                return Response(
                    {"error": "AI service returned invalid JSON"},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Check if the response has the expected structure
            if not ai_response.get('result') or not ai_response['result'].get('questions'):
                print(f"Error: AI service response does not contain questions: {ai_response}")
                return Response(
                    {"error": "AI service response does not contain questions"},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            # Create a new QuestionSet
            question_set = QuestionSet.objects.create(lesson=lesson)
            print(f"Created new QuestionSet with ID: {question_set.id}")

            # Create Question objects for each question in the response
            questions = []
            question_count = 0

            # Process the questions array
            for q_item in ai_response['result']['questions']:
                # Skip empty items
                if not q_item:
                    print("Skipping empty question item")
                    continue

                # Handle different response formats
                if isinstance(q_item, list):
                    # It's a list of questions or a list with question/answer
                    for q_subitem in q_item:
                        if not q_subitem:
                            continue

                        # If it's a dictionary with taxonomy levels
                        if isinstance(q_subitem, dict):
                            # Process each taxonomy level
                            for tax_level, questions_text in q_subitem.items():
                                if isinstance(questions_text, str):
                                    # Single question as string
                                    question_text = questions_text
                                    blooms_level = tax_level.lower() if tax_level.lower() in valid_cognitive_levels else cognitive_level

                                    # Create the question
                                    question = Question.objects.create(
                                        question_set=question_set,
                                        text=question_text,
                                        type=answer_style,
                                        blooms_taxonomy=blooms_level,
                                        difficulty='medium'
                                    )
                                    questions.append(question)
                                    question_count += 1
                                    print(f"Created question {question_count}: {blooms_level}")
                        elif isinstance(q_subitem, str):
                            # It's a simple string question
                            question = Question.objects.create(
                                question_set=question_set,
                                text=q_subitem,
                                type=answer_style,
                                blooms_taxonomy=cognitive_level,
                                difficulty='medium'
                            )
                            questions.append(question)
                            question_count += 1
                            print(f"Created string question {question_count}")
                elif isinstance(q_item, dict):
                    # It's a dictionary with taxonomy levels or question/answer
                    if 'question' in q_item:
                        # Standard question/answer format
                        question_text = q_item.get('question', '')
                        if 'answer' in q_item:
                            question_text += f"\n\nAnswer: {q_item['answer']}"

                        question = Question.objects.create(
                            question_set=question_set,
                            text=question_text,
                            type=answer_style,
                            blooms_taxonomy=cognitive_level,
                            difficulty='medium'
                        )
                        questions.append(question)
                        question_count += 1
                    else:
                        # Process each taxonomy level
                        for tax_level, questions_text in q_item.items():
                            blooms_level = tax_level.lower() if tax_level.lower() in valid_cognitive_levels else cognitive_level

                            if isinstance(questions_text, str):
                                # Single question as string
                                question = Question.objects.create(
                                    question_set=question_set,
                                    text=questions_text,
                                    type=answer_style,
                                    blooms_taxonomy=blooms_level,
                                    difficulty='medium'
                                )
                                questions.append(question)
                                question_count += 1
                                print(f"Created taxonomy question {question_count}: {blooms_level}")

            # If no questions were created, return an error
            if not questions:
                print("No valid questions found in the response")
                # Delete the empty question set
                question_set.delete()
                return Response(
                    {"error": "No valid questions could be extracted from the AI service response"},
                    status=status.HTTP_502_BAD_GATEWAY
                )

            print(f"Successfully created {len(questions)} questions")

            # Return the created QuestionSet and Questions
            from .serializers import QuestionSerializer

            return Response({
                "question_set": self.get_serializer(question_set).data,
                "questions": QuestionSerializer(questions, many=True).data,
                "count": len(questions)
            }, status=status.HTTP_201_CREATED)

        except requests.RequestException as e:
            print(f"Error calling AI service: {str(e)}")
            return Response(
                {
                    "error": f"Error calling AI service: {str(e)}",
                    "request_payload": payload,
                    "endpoint": endpoint
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {"error": f"Error generating questions: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @swagger_auto_schema(
        operation_description="Get question sets by lesson ID",
        manual_parameters=[
            openapi.Parameter(
                'lesson_id',
                openapi.IN_QUERY,
                description="Filter question sets by lesson ID",
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={200: QuestionSetSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    @require_api_key
    def by_lesson(self, request):
        lesson_id = request.query_params.get('lesson_id')
        if lesson_id:
            question_sets = QuestionSet.objects.filter(lesson=lesson_id)
        else:
            question_sets = QuestionSet.objects.all()
        serializer = self.get_serializer(question_sets, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get questions by question set ID",
        manual_parameters=[
            openapi.Parameter(
                'question_set_id',
                openapi.IN_QUERY,
                description="Filter questions by question set ID",
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={
            200: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'question_set': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'questions': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                    'count': openapi.Schema(type=openapi.TYPE_INTEGER)
                }
            ),
            404: 'Question set not found'
        }
    )
    @action(detail=False, methods=['get'])
    @require_api_key
    def by_question_set(self, request):
        question_set_id = request.query_params.get('question_set_id')
        if not question_set_id:
            return Response(
                {"error": "Missing required parameter: question_set_id"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            question_set = QuestionSet.objects.get(id=question_set_id)
        except QuestionSet.DoesNotExist:
            return Response(
                {"error": f"Question set with ID {question_set_id} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Use a custom serializer context to exclude nested questions
        serializer_context = {'exclude_questions': True}
        question_set_data = self.get_serializer(question_set, context=serializer_context).data

        # Get questions separately
        questions = Question.objects.filter(question_set=question_set_id)

        return Response({
            "question_set": question_set_data,
            "questions": QuestionSerializer(questions, many=True).data,
            "count": questions.count()
        })


class QuestionViewSet(viewsets.ModelViewSet):
    queryset = Question.objects.all()
    serializer_class = QuestionSerializer
    permission_classes = [permissions.IsAuthenticated]

    @swagger_auto_schema(
        operation_description="List all questions",
        responses={200: QuestionSerializer(many=True)},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Create a new question",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['question_set', 'text'],
            properties={
                'question_set': openapi.Schema(type=openapi.TYPE_INTEGER),
                'type': openapi.Schema(type=openapi.TYPE_STRING),
                'text': openapi.Schema(type=openapi.TYPE_STRING),
                'blooms_taxonomy': openapi.Schema(type=openapi.TYPE_STRING),
                'difficulty': openapi.Schema(type=openapi.TYPE_STRING),
                'outcome': openapi.Schema(type=openapi.TYPE_INTEGER),
            }
        ),
        responses={
            201: QuestionSerializer,
            400: 'Bad Request'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Retrieve a specific question by ID",
        responses={200: QuestionSerializer},
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Update a question",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'question_set': openapi.Schema(type=openapi.TYPE_INTEGER),
                'type': openapi.Schema(type=openapi.TYPE_STRING),
                'text': openapi.Schema(type=openapi.TYPE_STRING),
                'blooms_taxonomy': openapi.Schema(type=openapi.TYPE_STRING),
                'difficulty': openapi.Schema(type=openapi.TYPE_STRING),
                'outcome': openapi.Schema(type=openapi.TYPE_INTEGER),
            }
        ),
        responses={
            200: QuestionSerializer,
            400: 'Bad Request',
            404: 'Not Found'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Delete a question",
        responses={
            204: 'No Content',
            404: 'Not Found'
        },
        manual_parameters=[
            openapi.Parameter(
                'api-key',
                openapi.IN_HEADER,
                description="API Key for authentication",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @require_api_key
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

@swagger_auto_schema(
    method='post',
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['docsource_id'],
        properties={
            'docsource_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description="ID of the document resource to summarize"
            ),
            'lesson_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description="ID of the lesson to update with the summary (optional)"
            )
        }
    ),
    responses={
        200: openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'summary': openapi.Schema(type=openapi.TYPE_STRING),
                'status': openapi.Schema(type=openapi.TYPE_STRING),
                'lesson_updated': openapi.Schema(type=openapi.TYPE_BOOLEAN)
            }
        ),
        400: 'Bad Request',
        500: 'Internal Server Error'
    },
    operation_description="Summarize a document file using its docsource_id and optionally update a lesson",
    manual_parameters=[
        openapi.Parameter(
            'api-key',
            openapi.IN_HEADER,
            description="API Key for authentication",
            type=openapi.TYPE_STRING,
            required=True
        )
    ]
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])  # Temporarily allow any access for debugging
def summarize_file(request):
    """
    Summarize a document file using the AI service and optionally update a lesson
    """
    # Get API key from header
    api_key = request.headers.get('api-key')

    # Check if API key is provided
    if not api_key:
        return Response(
            {"error": "API key is required"},
            status=status.HTTP_401_UNAUTHORIZED
        )

    # Get docsource_id from request
    docsource_id = request.data.get('docsource_id')
    lesson_id = request.data.get('lesson_id')

    # Validate parameters
    if not docsource_id:
        return Response(
            {"error": "Missing required parameter: docsource_id"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Get AI service URL from environment
    ai_service_url = os.getenv('AI_SERVICE_URL')
    if not ai_service_url:
        return Response(
            {"error": "AI service URL not configured"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # Prepare request to AI service
    endpoint = f"{ai_service_url.rstrip('/')}/api/summarize-file/"
    headers = {
        'Content-Type': 'application/json',
        'api-key': api_key
    }
    payload = {
        'docsource_id': docsource_id
    }

    try:
        # Make request to AI service
        response = requests.post(endpoint, json=payload, headers=headers)

        # Check if the response status code indicates an error
        if response.status_code >= 400:
            return Response(
                {
                    "error": f"AI service returned error status: {response.status_code}",
                    "details": response.text[:500]
                },
                status=status.HTTP_502_BAD_GATEWAY
            )

        # Try to parse as JSON if possible
        try:
            ai_response = response.json()

            # If lesson_id is provided, update the lesson with the summary
            lesson_updated = False
            if lesson_id and 'summary' in ai_response:
                from .models import Lesson
                try:
                    lesson = Lesson.objects.get(id=lesson_id)
                    lesson.summary = ai_response['summary']
                    lesson.save()
                    lesson_updated = True
                except Lesson.DoesNotExist:
                    return Response(
                        {"error": f"Lesson with ID {lesson_id} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
                except Exception as e:
                    return Response(
                        {"error": f"Error updating lesson: {str(e)}"},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            # Add lesson_updated flag to the response
            response_data = ai_response
            response_data['lesson_updated'] = lesson_updated

            return Response(response_data)

        except ValueError:
            return Response(
                {"error": "AI service returned invalid JSON", "response": response.text[:500]},
                status=status.HTTP_502_BAD_GATEWAY
            )

    except requests.RequestException as e:
        return Response(
            {
                "error": f"Error calling AI service: {str(e)}",
                "request_payload": payload,
                "endpoint": endpoint
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response(
            {"error": f"Error summarizing file: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
