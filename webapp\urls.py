from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .api import (
    InstructorViewSet, CourseViewSet, LessonViewSet, OutcomeViewSet,
    QuestionSetViewSet, QuestionViewSet, summarize_file
)
from .auth import RegisterView, LoginView, GoogleLoginView

router = DefaultRouter()
router.register(r'instructors', InstructorViewSet)
router.register(r'courses', CourseViewSet)
router.register(r'lessons', LessonViewSet)
router.register(r'outcomes', OutcomeViewSet)
router.register(r'question-sets', QuestionSetViewSet)
router.register(r'questions', QuestionViewSet)

urlpatterns = [
    path('api/', include(router.urls)),

    # Authentication endpoints
    path('api/auth/register/', RegisterView.as_view(), name='register'),
    path('api/auth/login/', LoginView.as_view(), name='login'),
    path('api/auth/google/', GoogleLoginView.as_view(), name='google_login'),
    path('api/auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # File processing endpoints
    path('api/summarize-file/', summarize_file, name='summarize_file'),

    # Video transcription endpoint
    path('api/transcribe-video/', LessonViewSet.as_view({'post': 'transcribe_video'}), name='transcribe_video'),

    # Video summarization endpoint
    path('api/summarize-video/', LessonViewSet.as_view({'post': 'summarize_video'}), name='summarize_video'),
]
