# inpods_backend_AI

To run the project, follow these steps:

1. Clone the repository to your local machine.
2. Create a virtual environment and activate it:
   ```
   python -m venv venv
   source venv/bin/activate
   ```
3. Install the required dependencies using pip:
   ```
   pip install -r requirements.txt
   ```
4. Run the Django development server:
   ```
   python manage.py runserver 
   ```
5. Open your web browser and navigate to `http://localhost:8000` to access the application.
6. API documentation is available at `http://localhost:8000/swagger/` and `http://localhost:8000/redoc/`.

## API Documentation

All requests (except authentication endpoints) require an API key in the header:
```
api-key: your-api-key-here
```

### Authentication APIs

#### Register New Instructor
```http
POST /api/auth/register/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "username": "instructor1",
    "password": "securepass123",
    "department": "Computer Science"
}
```

#### Login with Email/Password
```http
POST /api/auth/login/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "securepass123"
}

Response:
{
    "refresh": "refresh-token",
    "access": "access-token",
    "email": "<EMAIL>",
    "username": "instructor1",
    "department": "Computer Science"
}
```

#### Login with Google
```http
POST /api/auth/google/
Content-Type: application/json

{
    "token": "google-oauth-token"
}

Response:
{
    "refresh": "refresh-token",
    "access": "access-token",
    "email": "<EMAIL>",
    "username": "instructor1",
    "department": "Computer Science"
}
```

#### Refresh Token
```http
POST /api/auth/refresh/
Content-Type: application/json

{
    "refresh": "your-refresh-token"
}

Response:
{
    "access": "new-access-token"
}
```

### Instructor APIs

#### List All Instructors
```http
GET /api/instructors/
```

#### Get Specific Instructor
```http
GET /api/instructors/{id}/
```

#### Create Instructor
```http
POST /api/instructors/
Content-Type: application/json

{
    "name": "John Doe",
    "department": "Computer Science"
}
```

#### Update Instructor
```http
PUT /api/instructors/{id}/
Content-Type: application/json

{
    "name": "John Doe",
    "department": "Computer Science"
}
```

#### Delete Instructor
```http
DELETE /api/instructors/{id}/
```

### Course APIs

#### List All Courses
```http
GET /api/courses/
```

#### Get Specific Course
```http
GET /api/courses/{id}/
```

#### Create Course
```http
POST /api/courses/
Content-Type: application/json

{
    "title": "Introduction to Programming",
    "description": "Learn basics of programming",
    "department": "Computer Science",
    "instructor_id": 1,
    "outcomes": [
        {
            "short_name": "LO1",
            "description": "Understand basic programming concepts"
        }
    ]
}
```

#### Update Course
```http
PUT /api/courses/{id}/
Content-Type: application/json

{
    "title": "Introduction to Programming",
    "description": "Learn basics of programming",
    "department": "Computer Science",
    "instructor_id": 1,
    "outcomes": [
        {
            "short_name": "LO1",
            "description": "Understand basic programming concepts"
        }
    ]
}
```

#### Delete Course
```http
DELETE /api/courses/{id}/
```

#### Get Courses by Instructor
```http
GET /api/courses/by_instructor/?instructor_id={id}
```

### Lesson APIs

#### List All Lessons
```http
GET /api/lessons/
```

#### Get Specific Lesson
```http
GET /api/lessons/{id}/
```

#### Create Lesson
```http
POST /api/lessons/
Content-Type: application/json

{
    "title": "Variables and Data Types",
    "description": "Introduction to variables and data types",
    "course": 1,
    "file": "https://example.com/lesson1.pdf"
}
```

#### Update Lesson
```http
PUT /api/lessons/{id}/
Content-Type: application/json

{
    "title": "Variables and Data Types",
    "description": "Introduction to variables and data types",
    "course": 1,
    "file": "https://example.com/lesson1.pdf"
}
```

#### Delete Lesson
```http
DELETE /api/lessons/{id}/
```

#### Get Lessons by Course
```http
GET /api/lessons/by_course/?course_id={id}
```

### Learning Outcome APIs

#### List All Outcomes
```http
GET /api/outcomes/
```

#### Get Specific Outcome
```http
GET /api/outcomes/{id}/
```

#### Create Outcome
```http
POST /api/outcomes/
Content-Type: application/json

{
    "short_name": "LO1",
    "description": "Understand basic programming concepts",
    "course": 1
}
```

#### Update Outcome
```http
PUT /api/outcomes/{id}/
Content-Type: application/json

{
    "short_name": "LO1",
    "description": "Understand basic programming concepts",
    "course": 1
}
```

#### Delete Outcome
```http
DELETE /api/outcomes/{id}/
```

#### Get Outcomes by Course
```http
GET /api/outcomes/by_course/?course_id={id}
```
